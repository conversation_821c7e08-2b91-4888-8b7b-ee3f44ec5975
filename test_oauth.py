#!/usr/bin/env python3
"""
Test script to check Google OAuth configuration
"""

import os
from flask import Flask
from flask_dance.contrib.google import make_google_blueprint

def test_oauth_config():
    """Test the OAuth configuration"""
    
    # Get credentials
    client_id = os.getenv("GOOGLE_CLIENT_ID", "169682567720-e500ldm2164qij7bse64krq1lvru5e5v.apps.googleusercontent.com")
    client_secret = os.getenv("GOOGLE_CLIENT_SECRET", "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")
    
    print("=== Google OAuth Configuration Test ===")
    print(f"Client ID: {client_id}")
    print(f"Client Secret: {'*' * len(client_secret) if client_secret else 'None'}")
    
    # Test Flask-Dance blueprint creation
    try:
        app = Flask(__name__)
        app.config['SECRET_KEY'] = 'test-key'
        
        google_bp = make_google_blueprint(
            client_id=client_id,
            client_secret=client_secret,
            scope=[
                "openid",
                "https://www.googleapis.com/auth/userinfo.email",
                "https://www.googleapis.com/auth/userinfo.profile"
            ],
            redirect_to="test_callback"
        )
        
        app.register_blueprint(google_bp, url_prefix="/login")
        
        print("✅ Flask-Dance blueprint created successfully")
        
        # Show expected redirect URI
        print(f"\n=== Required Redirect URI Configuration ===")
        print("Add these URIs to your Google Cloud Console OAuth client:")
        print("• http://127.0.0.1:5000/login/google/authorized")
        print("• http://localhost:5000/login/google/authorized")
        
        print(f"\n=== OAuth Login URL ===")
        with app.test_request_context():
            from flask import url_for
            login_url = url_for('google.login')
            print(f"Login URL: http://127.0.0.1:5000{login_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating OAuth blueprint: {e}")
        return False

if __name__ == "__main__":
    test_oauth_config()
