{% extends "base.html" %}
{% block title %}Complete Your Profile - ITian Club{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8 col-md-10">
        <div class="glass-card fade-in-up">
            <div class="text-center mb-4">
                <h2 class="display-5 fw-bold mb-3">Complete Your Profile</h2>
                <p class="lead">Please provide your details to continue with the quiz</p>
            </div>

            <!-- Profile Picture -->
            <div class="text-center mb-4">
                <div class="profile-picture-container">
                    <img src="{{ session.user_picture }}" alt="Profile Picture" class="profile-picture">
                    <div class="profile-picture-overlay">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
                <h5 class="mt-3">{{ session.user_name }}</h5>
                <p class="text-muted">{{ session.user_email }}</p>
            </div>

            <!-- Flash Messages -->
            {% with messages = get_flashed_messages() %}
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Profile Form -->
            <form method="POST" class="needs-validation" novalidate>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="urn" class="form-label">
                            <i class="fas fa-id-card me-2"></i>URN (Optional)
                        </label>
                        <input type="text" 
                               class="form-control form-control-lg" 
                               id="urn" 
                               name="urn" 
                               placeholder="Enter your URN"
                               autocomplete="off">
                        <div class="form-text">University Registration Number</div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="crn" class="form-label">
                            <i class="fas fa-id-badge me-2"></i>CRN (Optional)
                        </label>
                        <input type="text" 
                               class="form-control form-control-lg" 
                               id="crn" 
                               name="crn" 
                               placeholder="Enter your CRN"
                               autocomplete="off">
                        <div class="form-text">College Registration Number</div>
                    </div>
                </div>

                <div class="alert alert-info" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Note:</strong> At least one of URN or CRN is required to proceed.
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="branch" class="form-label">
                            <i class="fas fa-graduation-cap me-2"></i>Branch *
                        </label>
                        <select class="form-select form-select-lg" id="branch" name="branch" required>
                            <option value="">Select your branch</option>
                            <option value="Computer Science">Computer Science</option>
                            <option value="Information Technology">Information Technology</option>
                            <option value="Electronics & Communication">Electronics & Communication</option>
                            <option value="Mechanical Engineering">Mechanical Engineering</option>
                            <option value="Electrical Engineering">Electrical Engineering</option>
                            <option value="Civil Engineering">Civil Engineering</option>
                            <option value="Chemical Engineering">Chemical Engineering</option>
                            <option value="Biotechnology">Biotechnology</option>
                            <option value="Other">Other</option>
                        </select>
                        <div class="invalid-feedback">
                            Please select your branch.
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="year" class="form-label">
                            <i class="fas fa-calendar-alt me-2"></i>Year *
                        </label>
                        <select class="form-select form-select-lg" id="year" name="year" required>
                            <option value="">Select your year</option>
                            <option value="1">1st Year</option>
                            <option value="2">2nd Year</option>
                            <option value="3">3rd Year</option>
                            <option value="4">4th Year</option>
                        </select>
                        <div class="invalid-feedback">
                            Please select your year.
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="text-center mt-4">
                    <button type="submit" class="btn btn-premium btn-lg px-5">
                        <i class="fas fa-arrow-right me-2"></i>
                        Continue to Quiz
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    .profile-picture-container {
        position: relative;
        display: inline-block;
    }
    
    .profile-picture {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
    }
    
    .profile-picture:hover {
        transform: scale(1.05);
        border-color: var(--primary-blue);
    }
    
    .profile-picture-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .profile-picture-container:hover .profile-picture-overlay {
        opacity: 1;
    }
    
    .form-control, .form-select {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: var(--primary-blue);
        box-shadow: 0 0 0 0.2rem rgba(14, 165, 233, 0.25);
        color: white;
    }
    
    /* Fix dropdown options visibility */
    .form-select option {
        background: #1f2937 !important;
        color: #ffffff !important;
        padding: 0.5rem 1rem;
        border: none;
    }
    
    .form-select option:hover {
        background: #374151 !important;
        color: #ffffff !important;
    }
    
    .form-select option:checked {
        background: var(--primary-blue) !important;
        color: #ffffff !important;
    }
    
    /* Ensure dropdown is visible above other elements */
    .form-select {
        z-index: 10;
        position: relative;
    }
    
    /* Fix for webkit browsers */
    .form-select::-webkit-scrollbar {
        width: 8px;
    }
    
    .form-select::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
    }
    
    .form-select::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 4px;
    }
    
    .form-select::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.5);
    }
    
    .form-control::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }
    
    .form-label {
        color: white;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .form-text {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.875rem;
    }
    
    .alert {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        color: white;
    }
    
    .alert-info {
        background: rgba(14, 165, 233, 0.1);
        border-color: rgba(14, 165, 233, 0.3);
    }
    
    .alert-danger {
        background: rgba(239, 68, 68, 0.1);
        border-color: rgba(239, 68, 68, 0.3);
    }
    
    .btn-close {
        filter: invert(1);
    }
    
    @media (max-width: 768px) {
        .profile-picture {
            width: 100px;
            height: 100px;
        }
        
        .form-control, .form-select {
            font-size: 1rem;
        }
    }
</style>

<script>
    // Form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    
                    // Check if at least one of URN or CRN is provided
                    var urn = document.getElementById('urn').value.trim();
                    var crn = document.getElementById('crn').value.trim();
                    
                    if (!urn && !crn) {
                        event.preventDefault();
                        alert('Please provide either URN or CRN to continue.');
                        return false;
                    }
                    
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
</script>
{% endblock %}
